/**
 * 测试 API 修复
 * 模拟 Git LFS 指针文件的情况
 */

const fs = require('fs');
const path = require('path');

// 备份原文件
const originalFile = 'data/california-zip-codes.geojson';
const backupFile = originalFile + '.backup';

console.log('开始测试 Git LFS 修复...');

// 1. 备份原文件
if (fs.existsSync(originalFile)) {
  fs.copyFileSync(originalFile, backupFile);
  console.log('✅ 已备份原文件');
}

// 2. 创建模拟的 Git LFS 指针文件
const lfsContent = `version https://git-lfs.github.com/spec/v1
oid sha256:fedbe98f212b0aa751040ea4e0ac3f47b32a06f95d0494bd45701e41cf86e819
size 71207622
`;

fs.writeFileSync(originalFile, lfsContent);
console.log('✅ 已创建模拟 Git LFS 指针文件');

console.log('\n现在可以测试 API 端点：');
console.log('GET http://localhost:3000/api/zip-boundaries');
console.log('GET http://localhost:3000/api/debug/data-files?content=true');

console.log('\n测试完成后，运行以下命令恢复原文件：');
console.log('node restore-backup.js');

// 创建恢复脚本
const restoreScript = `const fs = require('fs');

const originalFile = 'data/california-zip-codes.geojson';
const backupFile = originalFile + '.backup';

if (fs.existsSync(backupFile)) {
  fs.copyFileSync(backupFile, originalFile);
  fs.unlinkSync(backupFile);
  console.log('✅ 已恢复原文件');
} else {
  console.log('❌ 备份文件不存在');
}
`;

fs.writeFileSync('restore-backup.js', restoreScript);
console.log('✅ 已创建恢复脚本 restore-backup.js');
