import type { DaycareGeoJSON } from "@/types/daycare";
import { MapManager } from "./MapManager";

/**
 * 主题感知的图层样式配置
 */
export interface LayerThemeConfig {
  zipAreas: {
    fillColor: string;
    fillOpacity: number;
    strokeColor: string;
    strokeWidth: number;
    strokeOpacity: number;
  };
  zipBoundaries: {
    fillColor: string;
    fillOpacity: number;
    lineColor: string;
    lineOpacity: number;
  };
  circles: {
    strokeColor: string;
    strokeWidth: number;
    colors: {
      low: string;
      medium: string;
      high: string;
      "very-high": string;
      default: string;
    };
  };
}

/**
 * 主题样式配置
 */
export const LAYER_THEMES: Record<"light" | "dark", LayerThemeConfig> = {
  light: {
    zipAreas: {
      fillColor: "#3b82f6",
      fillOpacity: 0.15,
      strokeColor: "#ffffff",
      strokeWidth: 1.5,
      strokeOpacity: 0.9,
    },
    zipBoundaries: {
      fillColor: "#3b82f6",
      fillOpacity: 0.1,
      lineColor: "#3b82f6",
      lineOpacity: 0.8,
    },
    circles: {
      strokeColor: "#ffffff",
      strokeWidth: 2,
      colors: {
        low: "#22c55e",
        medium: "#eab308",
        high: "#f97316",
        "very-high": "#ef4444",
        default: "#6b7280",
      },
    },
  },
  dark: {
    zipAreas: {
      fillColor: "#60a5fa",
      fillOpacity: 0.2,
      strokeColor: "#e5e7eb",
      strokeWidth: 1.5,
      strokeOpacity: 0.8,
    },
    zipBoundaries: {
      fillColor: "#60a5fa",
      fillOpacity: 0.15,
      lineColor: "#60a5fa",
      lineOpacity: 0.9,
    },
    circles: {
      strokeColor: "#e5e7eb",
      strokeWidth: 2,
      colors: {
        low: "#34d399",
        medium: "#fbbf24",
        high: "#fb923c",
        "very-high": "#f87171",
        default: "#9ca3af",
      },
    },
  },
};

/**
 * 图层管理器
 * 负责管理地图图层的添加、移除和样式配置
 * 支持主题适配
 */
export class LayerManager {
  private mapManager: MapManager;
  private currentTheme: "light" | "dark" = "dark";
  private cachedZipData: any = null; // 缓存ZIP数据
  private cachedPointData: any = null; // 缓存点数据

  constructor(mapManager: MapManager) {
    this.mapManager = mapManager;
    this.currentTheme = this.getCurrentTheme();
    this.setupThemeObserver();
  }

  /**
   * 获取当前主题
   */
  private getCurrentTheme(): "light" | "dark" {
    if (typeof window === "undefined") {
      return "dark";
    }

    const theme = document.documentElement.getAttribute("data-theme");
    return (theme as "light" | "dark") || "dark";
  }

  /**
   * 快速重新应用缓存的图层（用于主题切换优化）
   */
  fastReapplyLayers(): boolean {
    const map = this.mapManager.getMap();
    if (!map) {
      return false;
    }

    try {
      // 批量操作，减少重绘次数
      map.getCanvas().style.visibility = "hidden";

      // 如果有缓存的ZIP数据，快速重新应用
      if (this.cachedZipData) {
        this.fastAddZipAreas(this.cachedZipData);
        map.getCanvas().style.visibility = "visible";
        return true;
      }

      // 如果有缓存的点数据，快速重新应用
      if (this.cachedPointData) {
        this.fastAddPointData(this.cachedPointData);
        map.getCanvas().style.visibility = "visible";
        return true;
      }

      map.getCanvas().style.visibility = "visible";
      return false;
    } catch (error) {
      console.error("快速重新应用图层失败:", error);
      map.getCanvas().style.visibility = "visible";
      return false;
    }
  }

  /**
   * 快速添加ZIP区域（优化版本）
   */
  private fastAddZipAreas(mergedData: any): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 只移除图层，保留数据源以提高性能
    this.removeLayersOnly();

    // 检查数据源是否存在，如果不存在才添加
    if (!map.getSource("daycare-zip-areas")) {
      this.mapManager.addSource("daycare-zip-areas", {
        type: "geojson",
        data: mergedData,
      });
    } else {
      // 更新现有数据源
      const source = map.getSource("daycare-zip-areas") as any;
      if (source && source.setData) {
        source.setData(mergedData);
      }
    }

    // 快速添加图层
    this.addZipAreaLayers();
  }

  /**
   * 快速添加点数据（优化版本）
   */
  private fastAddPointData(geoJsonData: any): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 只移除图层，保留数据源
    this.removeLayersOnly();

    // 检查数据源是否存在
    if (!map.getSource("daycare-data-2023")) {
      this.mapManager.addSource("daycare-data-2023", {
        type: "geojson",
        data: geoJsonData,
      });
    } else {
      // 更新现有数据源
      const source = map.getSource("daycare-data-2023") as any;
      if (source && source.setData) {
        source.setData(geoJsonData);
      }
    }

    // 快速添加图层
    this.addPointLayers();
  }

  /**
   * 只移除图层，保留数据源（性能优化）
   */
  private removeLayersOnly(): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    const layersToRemove = [
      "daycare-zip-fill",
      "daycare-zip-stroke",
      "daycare-circles",
      "daycare-circles-stroke",
      "zip-boundaries-fill",
      "zip-boundaries-stroke",
    ];

    layersToRemove.forEach((layerId) => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });
  }

  /**
   * 快速添加ZIP区域图层
   */
  private addZipAreaLayers(): void {
    const themeConfig = LAYER_THEMES[this.currentTheme];

    // 添加填充图层
    this.mapManager.addLayer({
      id: "daycare-zip-fill",
      type: "fill",
      source: "daycare-zip-areas",
      paint: {
        "fill-color": [
          "match",
          ["get", "saturation_level"],
          "low",
          themeConfig.circles.colors.low,
          "medium",
          themeConfig.circles.colors.medium,
          "high",
          themeConfig.circles.colors.high,
          "very-high",
          themeConfig.circles.colors["very-high"],
          themeConfig.circles.colors.default,
        ],
        "fill-opacity": [
          "interpolate",
          ["linear"],
          ["get", "saturation"],
          0,
          themeConfig.zipAreas.fillOpacity * 0.5,
          1,
          themeConfig.zipAreas.fillOpacity * 0.7,
          2,
          themeConfig.zipAreas.fillOpacity,
          5,
          themeConfig.zipAreas.fillOpacity * 1.2,
          50,
          themeConfig.zipAreas.fillOpacity * 1.5,
        ],
      },
    });

    // 添加边框图层
    this.mapManager.addLayer({
      id: "daycare-zip-stroke",
      type: "line",
      source: "daycare-zip-areas",
      paint: {
        "line-color": themeConfig.zipAreas.strokeColor,
        "line-width": themeConfig.zipAreas.strokeWidth,
        "line-opacity": themeConfig.zipAreas.strokeOpacity,
      },
    });
  }

  /**
   * 快速添加点图层
   */
  private addPointLayers(): void {
    const themeConfig = LAYER_THEMES[this.currentTheme];

    // 添加圆点图层
    this.mapManager.addLayer({
      id: "daycare-circles",
      type: "circle",
      source: "daycare-data-2023",
      paint: {
        "circle-radius": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          2,
          10,
          4,
          14,
          8,
        ],
        "circle-color": [
          "match",
          ["get", "saturation_level"],
          "low",
          themeConfig.circles.colors.low,
          "medium",
          themeConfig.circles.colors.medium,
          "high",
          themeConfig.circles.colors.high,
          "very-high",
          themeConfig.circles.colors["very-high"],
          themeConfig.circles.colors.default,
        ],
        "circle-stroke-color": themeConfig.circles.strokeColor,
        "circle-stroke-width": themeConfig.circles.strokeWidth,
      },
    });
  }

  /**
   * 设置主题观察器
   */
  private setupThemeObserver(): void {
    if (typeof window === "undefined") {
      return;
    }

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-theme"
        ) {
          const newTheme = this.getCurrentTheme();
          if (newTheme !== this.currentTheme) {
            this.currentTheme = newTheme;
            this.updateLayerStyles();
          }
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["data-theme"],
    });
  }

  /**
   * 更新图层样式以适应新主题
   */
  private updateLayerStyles(): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    const themeConfig = LAYER_THEMES[this.currentTheme];

    // 更新ZIP区域图层样式
    if (this.mapManager.hasLayer("daycare-zip-fill")) {
      // 更新饱和度颜色映射（这是关键！）
      map.setPaintProperty("daycare-zip-fill", "fill-color", [
        "match",
        ["get", "saturation_level"],
        "low",
        themeConfig.circles.colors.low,
        "medium",
        themeConfig.circles.colors.medium,
        "high",
        themeConfig.circles.colors.high,
        "very-high",
        themeConfig.circles.colors["very-high"],
        themeConfig.circles.colors.default,
      ]);

      // 更新透明度
      map.setPaintProperty("daycare-zip-fill", "fill-opacity", [
        "interpolate",
        ["linear"],
        ["get", "saturation"],
        0,
        themeConfig.zipAreas.fillOpacity * 0.5,
        1,
        themeConfig.zipAreas.fillOpacity * 0.7,
        2,
        themeConfig.zipAreas.fillOpacity,
        5,
        themeConfig.zipAreas.fillOpacity * 1.2,
        50,
        themeConfig.zipAreas.fillOpacity * 1.5,
      ]);
    }

    if (this.mapManager.hasLayer("daycare-zip-stroke")) {
      map.setPaintProperty(
        "daycare-zip-stroke",
        "line-color",
        themeConfig.zipAreas.strokeColor
      );
      map.setPaintProperty(
        "daycare-zip-stroke",
        "line-opacity",
        themeConfig.zipAreas.strokeOpacity
      );
    }

    // 更新ZIP边界图层样式
    if (this.mapManager.hasLayer("zip-boundaries-fill")) {
      map.setPaintProperty(
        "zip-boundaries-fill",
        "fill-color",
        themeConfig.zipBoundaries.fillColor
      );
      map.setPaintProperty(
        "zip-boundaries-fill",
        "fill-opacity",
        themeConfig.zipBoundaries.fillOpacity
      );
    }

    if (this.mapManager.hasLayer("zip-boundaries-line")) {
      map.setPaintProperty(
        "zip-boundaries-line",
        "line-color",
        themeConfig.zipBoundaries.lineColor
      );
      map.setPaintProperty(
        "zip-boundaries-line",
        "line-opacity",
        themeConfig.zipBoundaries.lineOpacity
      );
    }

    // 更新圆点图层样式
    if (this.mapManager.hasLayer("daycare-circles")) {
      map.setPaintProperty(
        "daycare-circles",
        "circle-stroke-color",
        themeConfig.circles.strokeColor
      );
      map.setPaintProperty("daycare-circles", "circle-color", [
        "match",
        ["get", "saturation_level"],
        "low",
        themeConfig.circles.colors.low,
        "medium",
        themeConfig.circles.colors.medium,
        "high",
        themeConfig.circles.colors.high,
        "very-high",
        themeConfig.circles.colors["very-high"],
        themeConfig.circles.colors.default,
      ]);
    }
  }

  /**
   * 将ZIP区域数据添加到地图
   */
  addZipAreasToMap(mergedData: any): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 缓存数据以提高主题切换性能
    this.cachedZipData = mergedData;

    // 检查并移除已存在的图层和数据源
    this.removeExistingLayers();

    // 添加数据源
    this.mapManager.addSource("daycare-zip-areas", {
      type: "geojson",
      data: mergedData,
    });

    // 添加填充图层 - 根据饱和度显示不同颜色（使用主题配置）
    const themeConfig = LAYER_THEMES[this.currentTheme];

    this.mapManager.addLayer({
      id: "daycare-zip-fill",
      type: "fill",
      source: "daycare-zip-areas",
      paint: {
        // 根据饱和度等级设置颜色（使用主题配置）
        "fill-color": [
          "match",
          ["get", "saturation_level"],
          "low",
          themeConfig.circles.colors.low,
          "medium",
          themeConfig.circles.colors.medium,
          "high",
          themeConfig.circles.colors.high,
          "very-high",
          themeConfig.circles.colors["very-high"],
          themeConfig.circles.colors.default,
        ],
        // 根据饱和度调整透明度
        "fill-opacity": [
          "interpolate",
          ["linear"],
          ["get", "saturation"],
          0,
          themeConfig.zipAreas.fillOpacity * 0.5, // 最低饱和度
          1,
          themeConfig.zipAreas.fillOpacity * 0.7, // 低饱和度
          2,
          themeConfig.zipAreas.fillOpacity, // 中等饱和度
          5,
          themeConfig.zipAreas.fillOpacity * 1.2, // 高饱和度
          50,
          themeConfig.zipAreas.fillOpacity * 1.5, // 极高饱和度
        ],
      },
    });

    // 添加边界线图层（使用主题配置）
    this.mapManager.addLayer({
      id: "daycare-zip-stroke",
      type: "line",
      source: "daycare-zip-areas",
      paint: {
        "line-color": themeConfig.zipAreas.strokeColor,
        "line-width": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          themeConfig.zipAreas.strokeWidth * 0.3, // 低缩放级别时更细
          10,
          themeConfig.zipAreas.strokeWidth * 0.7, // 中等缩放级别
          14,
          themeConfig.zipAreas.strokeWidth, // 高缩放级别时更粗
        ],
        "line-opacity": themeConfig.zipAreas.strokeOpacity,
      },
    });

    // 添加ZIP码文本图层
    this.mapManager.addLayer({
      id: "daycare-zip-labels",
      type: "symbol",
      source: "daycare-zip-areas",
      layout: {
        "text-field": ["get", "zip_code"],
        "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
        "text-size": [
          "interpolate",
          ["linear"],
          ["zoom"],
          8,
          10, // 低缩放级别时较小
          10,
          12, // 中等缩放级别
          12,
          14, // 高缩放级别时较大
          14,
          16,
        ],
        "text-anchor": "center",
        "text-justify": "center",
        "text-allow-overlap": false,
        "text-ignore-placement": false,
        "symbol-placement": "point",
      },
      paint: {
        "text-color": "#ffffff",
        "text-halo-color": "#000000",
        "text-halo-width": 1.5,
        "text-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          8,
          0.6, // 低缩放级别时较透明
          10,
          0.8, // 中等缩放级别
          12,
          1.0, // 高缩放级别时完全不透明
        ],
      },
      // 只在较高缩放级别显示文本，避免过于拥挤
      minzoom: 9,
    });
  }

  /**
   * 将数据添加到地图（圆点模式 - 备用）
   */
  addDataToMap(geoJsonData: DaycareGeoJSON): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 缓存数据以提高主题切换性能
    this.cachedPointData = geoJsonData;

    // 检查并移除已存在的图层和数据源
    this.removeExistingLayers();

    const themeConfig = LAYER_THEMES[this.currentTheme];

    // 添加数据源
    this.mapManager.addSource("daycare-data-2023", {
      type: "geojson",
      data: geoJsonData,
    });

    // 添加圆点图层（使用主题配置）
    this.mapManager.addLayer({
      id: "daycare-circles",
      type: "circle",
      source: "daycare-data-2023",
      paint: {
        // 根据饱和度调整圆点大小
        "circle-radius": [
          "interpolate",
          ["linear"],
          ["get", "saturation"],
          0,
          8, // 最小饱和度 -> 8px
          1,
          10, // 低饱和度 -> 10px
          5,
          14, // 中等饱和度 -> 14px
          10,
          18, // 高饱和度 -> 18px
          20,
          22, // 很高饱和度 -> 22px
          50,
          26, // 极高饱和度 -> 26px
        ],
        // 根据饱和度等级设置颜色（使用主题配置）
        "circle-color": [
          "match",
          ["get", "saturation_level"],
          "low",
          themeConfig.circles.colors.low,
          "medium",
          themeConfig.circles.colors.medium,
          "high",
          themeConfig.circles.colors.high,
          "very-high",
          themeConfig.circles.colors["very-high"],
          themeConfig.circles.colors.default,
        ],
        "circle-opacity": 0.8,
        "circle-stroke-width": themeConfig.circles.strokeWidth,
        "circle-stroke-color": themeConfig.circles.strokeColor,
      },
    });
  }

  /**
   * 将ZIP边界添加到地图
   */
  async addZipBoundariesToMap(geoJsonData: any): Promise<void> {
    const map = this.mapManager.getMap();
    if (!map || !geoJsonData) {
      return;
    }

    const themeConfig = LAYER_THEMES[this.currentTheme];

    // 使用 requestAnimationFrame 来避免阻塞UI
    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 检查并移除已存在的ZIP边界图层和数据源
    this.mapManager.removeLayer("zip-boundaries-fill");
    this.mapManager.removeLayer("zip-boundaries-line");
    this.mapManager.removeSource("zip-boundaries");

    // 添加ZIP边界数据源
    this.mapManager.addSource("zip-boundaries", {
      type: "geojson",
      data: geoJsonData,
      // 性能优化选项
      lineMetrics: false,
      generateId: true,
      buffer: 0, // 减少缓冲区
      tolerance: 0.375, // 简化几何形状
    });

    // 使用 requestAnimationFrame 分批添加图层
    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 添加填充图层（半透明，使用主题配置）
    this.mapManager.addLayer({
      id: "zip-boundaries-fill",
      type: "fill",
      source: "zip-boundaries",
      paint: {
        "fill-color": themeConfig.zipBoundaries.fillColor,
        "fill-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          themeConfig.zipBoundaries.fillOpacity * 0.3, // 低缩放级别时更透明
          10,
          themeConfig.zipBoundaries.fillOpacity * 0.7, // 中等缩放级别
          14,
          themeConfig.zipBoundaries.fillOpacity, // 高缩放级别时更明显
        ],
      },
      // 只在特定缩放级别显示以提高性能
      minzoom: 6,
    });

    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 添加边界线图层（使用主题配置）
    this.mapManager.addLayer({
      id: "zip-boundaries-line",
      type: "line",
      source: "zip-boundaries",
      paint: {
        "line-color": themeConfig.zipBoundaries.lineColor,
        "line-width": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.3, // 低缩放级别时更细
          10,
          0.8, // 中等缩放级别
          14,
          1.5, // 高缩放级别时更粗
        ],
        "line-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          themeConfig.zipBoundaries.lineOpacity * 0.5, // 低缩放级别时更透明
          10,
          themeConfig.zipBoundaries.lineOpacity * 0.8, // 中等缩放级别
          14,
          themeConfig.zipBoundaries.lineOpacity, // 高缩放级别时更明显
        ],
      },
      minzoom: 6,
    });
  }

  /**
   * 隐藏ZIP边界
   */
  hideZipBoundaries(): void {
    this.mapManager.removeLayer("zip-boundaries-fill");
    this.mapManager.removeLayer("zip-boundaries-line");
    this.mapManager.removeSource("zip-boundaries");
  }

  /**
   * 移除已存在的图层和数据源
   */
  removeExistingLayers(): void {
    // 移除ZIP区域图层
    this.mapManager.removeLayer("daycare-zip-labels");
    this.mapManager.removeLayer("daycare-zip-stroke");
    this.mapManager.removeLayer("daycare-zip-fill");
    this.mapManager.removeSource("daycare-zip-areas");

    // 移除圆点图层
    this.mapManager.removeLayer("daycare-circles");
    this.mapManager.removeSource("daycare-data-2023");
  }
}
