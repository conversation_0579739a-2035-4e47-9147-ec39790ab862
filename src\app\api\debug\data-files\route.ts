import fs from "fs";
import path from "path";
import { NextRequest, NextResponse } from "next/server";

/**
 * GET /api/debug/data-files
 * 诊断数据文件状态
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const checkContent = searchParams.get("content") === "true";

    const files = [
      "data/california-zip-codes.geojson",
      "data/processed_2023/api_data_2023.json",
      "data/processed_2023/daycare_data_2023.geojson",
      "data/processed_2023/daycare_properties_2023.json",
    ];

    const results = [];

    for (const file of files) {
      const filePath = path.join(process.cwd(), file);
      const result: any = {
        file,
        exists: fs.existsSync(filePath),
      };

      if (result.exists) {
        try {
          const stats = fs.statSync(filePath);
          result.size = stats.size;
          result.modified = stats.mtime.toISOString();

          if (checkContent) {
            const content = fs.readFileSync(filePath, "utf8");
            result.contentLength = content.length;
            result.firstChars = content.substring(0, 100);
            result.lastChars = content.substring(
              Math.max(0, content.length - 100)
            );

            // 尝试解析 JSON
            try {
              const parsed = JSON.parse(content);
              result.jsonValid = true;
              if (parsed.features) {
                result.featuresCount = parsed.features.length;
              } else if (Array.isArray(parsed)) {
                result.arrayLength = parsed.length;
              } else if (parsed.properties) {
                result.propertiesCount = Object.keys(parsed.properties).length;
              }
            } catch (parseError) {
              result.jsonValid = false;
              result.parseError =
                parseError instanceof Error
                  ? parseError.message
                  : "Unknown error";
            }
          }
        } catch (error) {
          result.error =
            error instanceof Error ? error.message : "Unknown error";
        }
      }

      results.push(result);
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      cwd: process.cwd(),
      nodeVersion: process.version,
      platform: process.platform,
      files: results,
    });
  } catch (error) {
    console.error("Debug endpoint error:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
