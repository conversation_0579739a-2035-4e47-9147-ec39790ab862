"use client";

import { useState, useEffect, useCallback } from "react";
import type {
  MapState,
  DaycareData,
  DaycareStats,
  SearchResult,
  ApiResponse,
} from "@/types/daycare";

/**
 * 托儿所地图数据管理Hook
 */
export const useDaycareMap = () => {
  // 地图状态
  const [mapState, setMapState] = useState<MapState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    data: null,
    stats: null,
    selectedZip: null,
    searchQuery: "",
    searchResults: [],
  });

  // 搜索相关状态
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);

  // 加载数据
  const loadData = useCallback(async () => {
    console.log("开始加载数据...");
    setMapState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log("发送API请求...");
      // 并行加载数据和统计信息
      const [dataResponse, statsResponse] = await Promise.all([
        fetch("/api/daycare-data-2023"),
        fetch("/api/daycare-data-2023/stats"),
      ]);

      console.log("API响应状态:", {
        dataOk: dataResponse.ok,
        statsOk: statsResponse.ok,
        dataStatus: dataResponse.status,
        statsStatus: statsResponse.status,
      });

      // 检查响应状态
      if (!dataResponse.ok || !statsResponse.ok) {
        throw new Error(
          `数据加载失败 - Data: ${dataResponse.status}, Stats: ${statsResponse.status}`
        );
      }

      const dataResult: ApiResponse<DaycareData[]> = await dataResponse.json();
      const statsResult: ApiResponse<DaycareStats> = await statsResponse.json();

      console.log("API响应结果:", {
        dataSuccess: dataResult.success,
        statsSuccess: statsResult.success,
        dataCount: dataResult.data?.length,
        statsData: statsResult.data,
      });

      // 检查API响应
      if (!dataResult.success) {
        throw new Error(dataResult.error || "数据加载失败");
      }

      if (!statsResult.success) {
        throw new Error(statsResult.error || "统计信息加载失败");
      }

      console.log("数据加载成功，更新状态...");
      // 更新状态
      setMapState((prev) => ({
        ...prev,
        isLoading: false,
        data: dataResult.data,
        stats: statsResult.data,
        error: null,
      }));
    } catch (error) {
      console.error("Error loading daycare data:", error);
      setMapState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "数据加载失败",
      }));
    }
  }, []);

  // 搜索地址
  const searchAddress = useCallback(
    async (query: string): Promise<SearchResult[]> => {
      const accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

      if (!accessToken) {
        throw new Error("Mapbox access token is required");
      }

      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          query
        )}.json?` +
          `access_token=${accessToken}&` +
          `country=US&` +
          `bbox=-124.4,32.5,-114.1,42.0&` + // 限制在加州范围
          `limit=5`
      );

      if (!response.ok) {
        throw new Error("搜索请求失败");
      }

      const data = await response.json();

      return data.features.map((feature: any) => ({
        id: feature.id,
        place_name: feature.place_name,
        center: feature.center,
        bbox: feature.bbox,
        context: feature.context,
      }));
    },
    []
  );

  // 处理搜索
  const handleSearch = useCallback(
    async (query: string) => {
      setSearchQuery(query);

      if (!query.trim()) {
        setSearchResults([]);
        // 清除搜索高亮
        window.dispatchEvent(new CustomEvent("clearSearchHighlight"));
        return;
      }

      try {
        const results = await searchAddress(query);
        setSearchResults(results);

        // 更新地图状态
        setMapState((prev) => ({
          ...prev,
          searchQuery: query,
          searchResults: results,
        }));
      } catch (error) {
        console.error("Search error:", error);
        setSearchResults([]);
      }
    },
    [searchAddress]
  );

  // 从搜索结果中提取ZIP码
  const extractZipCodeFromResult = useCallback(
    (result: SearchResult): string | undefined => {
      // 尝试从place_name中提取ZIP码
      const zipMatch = result.place_name.match(/\b\d{5}\b/);
      if (zipMatch) {
        return zipMatch[0];
      }

      // 尝试从context中提取ZIP码
      if (result.context) {
        for (const contextItem of result.context) {
          const zipMatch = contextItem.text.match(/\b\d{5}\b/);
          if (zipMatch) {
            return zipMatch[0];
          }
        }
      }

      return undefined;
    },
    []
  );

  // 处理搜索结果选择
  const handleSearchResultSelect = useCallback((result: SearchResult) => {
    setSearchQuery(result.place_name);
    setSearchResults([]);

    // 更新地图状态
    setMapState((prev) => ({
      ...prev,
      searchQuery: result.place_name,
      searchResults: [],
    }));

    // 提取ZIP码（如果存在）
    const zipCode = extractZipCodeFromResult(result);

    // 触发地图飞行到选中位置
    window.dispatchEvent(
      new CustomEvent("mapFlyTo", {
        detail: { coordinates: result.center, zoom: 12 },
      })
    );

    // 触发搜索高亮
    window.dispatchEvent(
      new CustomEvent("searchHighlight", {
        detail: {
          coordinates: result.center,
          zipCode: zipCode,
          placeName: result.place_name,
        },
      })
    );
  }, []);

  // 处理地图加载完成
  const handleMapLoad = useCallback(() => {
    setMapState((prev) => ({ ...prev, isLoaded: true }));
  }, []);

  // 处理地图错误
  const handleMapError = useCallback((error: Error) => {
    setMapState((prev) => ({
      ...prev,
      error: error.message,
      isLoading: false,
    }));
  }, []);

  // 选择ZIP码
  const selectZip = useCallback((zipCode: string | null) => {
    setMapState((prev) => ({ ...prev, selectedZip: zipCode }));
  }, []);

  // 获取特定ZIP码数据
  const getZipData = useCallback(
    async (zipCode: string): Promise<DaycareData | null> => {
      try {
        const response = await fetch(`/api/daycare-data-2023?zip=${zipCode}`);
        const result: ApiResponse<DaycareData> = await response.json();

        if (result.success) {
          return result.data;
        } else {
          console.error("Error fetching ZIP data:", result.error);
          return null;
        }
      } catch (error) {
        console.error("Error fetching ZIP data:", error);
        return null;
      }
    },
    []
  );

  // 重置状态
  const resetState = useCallback(() => {
    setMapState({
      isLoaded: false,
      isLoading: false,
      error: null,
      data: null,
      stats: null,
      selectedZip: null,
      searchQuery: "",
      searchResults: [],
    });
    setSearchQuery("");
    setSearchResults([]);
  }, []);

  // 重新加载数据
  const reloadData = useCallback(() => {
    resetState();
    loadData();
  }, [resetState, loadData]);

  // 初始化时加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 返回状态和方法
  return {
    // 状态
    mapState,
    searchQuery,
    searchResults,

    // 数据操作
    loadData,
    reloadData,
    getZipData,

    // 搜索操作
    handleSearch,
    handleSearchResultSelect,

    // 地图操作
    handleMapLoad,
    handleMapError,
    selectZip,

    // 工具方法
    resetState,

    // 计算属性
    isDataReady: mapState.data !== null && mapState.stats !== null,
    hasError: mapState.error !== null,
    isLoading: mapState.isLoading,
  };
};
