import mapboxgl from "mapbox-gl";
import type { DaycareFeature } from "@/types/daycare";
import { MapManager } from "./MapManager";

/**
 * 事件处理器
 * 负责处理地图点击、悬停等交互事件
 */
export class EventHandler {
  private mapManager: MapManager;
  private popupRef: mapboxgl.Popup | null = null;
  private highlightedFeatures: Set<string> = new Set();
  private searchMarker: mapboxgl.Marker | null = null;
  private onPopupCreate?: (
    popup: mapboxgl.Popup,
    feature: DaycareFeature
  ) => void;

  constructor(mapManager: MapManager) {
    this.mapManager = mapManager;
  }

  /**
   * 设置弹窗创建回调
   */
  setPopupCreateCallback(
    callback: (popup: mapboxgl.Popup, feature: DaycareFeature) => void
  ): void {
    this.onPopupCreate = callback;
  }

  /**
   * 初始化ZIP区域事件
   */
  initializeZipAreaEvents(): void {
    // 添加点击事件
    this.mapManager.onClick(
      "daycare-zip-fill",
      this.handleZipAreaClick.bind(this)
    );

    // 添加鼠标悬停效果
    this.mapManager.onMouseEnter("daycare-zip-fill", () => {
      this.mapManager.setCursor("pointer");
    });

    this.mapManager.onMouseLeave("daycare-zip-fill", () => {
      this.mapManager.setCursor("");
    });
  }

  /**
   * 初始化圆点事件
   */
  initializeCircleEvents(): void {
    // 添加点击事件
    this.mapManager.onClick(
      "daycare-circles",
      this.handleCircleClick.bind(this)
    );

    // 添加鼠标悬停效果
    this.mapManager.onMouseEnter("daycare-circles", () => {
      this.mapManager.setCursor("pointer");
    });

    this.mapManager.onMouseLeave("daycare-circles", () => {
      this.mapManager.setCursor("");
    });
  }

  /**
   * 处理ZIP区域点击事件
   */
  private handleZipAreaClick(
    e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
  ): void {
    if (!e.features || e.features.length === 0) {
      return;
    }

    const feature = e.features[0] as any as DaycareFeature;
    this.createPopup(e.lngLat, feature);
  }

  /**
   * 处理圆点点击事件
   */
  private handleCircleClick(
    e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
  ): void {
    if (!e.features || e.features.length === 0) {
      return;
    }

    const feature = e.features[0] as any as DaycareFeature;
    this.createPopup(e.lngLat, feature);
  }

  /**
   * 创建弹窗
   */
  private createPopup(lngLat: mapboxgl.LngLat, feature: DaycareFeature): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 关闭现有弹窗
    if (this.popupRef) {
      this.popupRef.remove();
    }

    // 创建新弹窗
    const popup = new mapboxgl.Popup({
      offset: 25,
      closeButton: true,
      closeOnClick: true,
    }).setLngLat(lngLat);

    // 使用 setTimeout 确保弹窗在正确位置显示
    setTimeout(() => {
      popup.addTo(map);
      this.popupRef = popup;

      // 调用弹窗创建回调
      if (this.onPopupCreate) {
        this.onPopupCreate(popup, feature);
      }
    }, 0);

    // 弹窗关闭事件
    popup.on("close", () => {
      this.popupRef = null;
    });
  }

  /**
   * 初始化飞行到位置事件监听
   */
  initializeFlyToEvents(): () => void {
    const handleMapFlyTo = (event: CustomEvent) => {
      const { coordinates, zoom } = event.detail;
      this.mapManager.flyTo(coordinates, zoom);
    };

    window.addEventListener("mapFlyTo", handleMapFlyTo as EventListener);

    // 返回清理函数
    return () => {
      window.removeEventListener("mapFlyTo", handleMapFlyTo as EventListener);
    };
  }

  /**
   * 关闭当前弹窗
   */
  closePopup(): void {
    if (this.popupRef) {
      this.popupRef.remove();
      this.popupRef = null;
    }
  }

  /**
   * 获取当前弹窗
   */
  getCurrentPopup(): mapboxgl.Popup | null {
    return this.popupRef;
  }

  /**
   * 高亮搜索位置
   * @param coordinates 搜索位置的经纬度
   * @param zipCode 可选的ZIP码，用于高亮对应的区域
   */
  highlightSearchLocation(
    coordinates: [number, number],
    zipCode?: string
  ): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 移除之前的搜索标记
    this.clearSearchHighlight();

    // 创建搜索标记
    const markerElement = document.createElement("div");
    markerElement.className = "search-marker";
    markerElement.innerHTML = `
      <div class="search-marker-inner">
        <div class="search-marker-pulse"></div>
        <div class="search-marker-icon">📍</div>
      </div>
    `;

    this.searchMarker = new mapboxgl.Marker(markerElement)
      .setLngLat(coordinates)
      .addTo(map);

    // 如果有ZIP码，高亮对应的区域
    if (zipCode) {
      this.highlightZipArea(zipCode);
    }

    // 添加搜索标记样式
    this.addSearchMarkerStyles();
  }

  /**
   * 高亮ZIP区域
   * @param zipCode ZIP码
   */
  private highlightZipArea(zipCode: string): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 清除之前的高亮
    this.clearZipHighlight();

    // 添加高亮过滤器
    if (map.getLayer("daycare-zip-fill")) {
      // 创建高亮图层
      if (!map.getLayer("search-highlight-fill")) {
        map.addLayer({
          id: "search-highlight-fill",
          type: "fill",
          source: "daycare-zip-areas",
          paint: {
            "fill-color": "#ff6b35",
            "fill-opacity": 0.4,
          },
          filter: ["==", ["get", "zip_code"], zipCode],
        });

        map.addLayer({
          id: "search-highlight-line",
          type: "line",
          source: "daycare-zip-areas",
          paint: {
            "line-color": "#ff6b35",
            "line-width": 3,
            "line-opacity": 0.8,
          },
          filter: ["==", ["get", "zip_code"], zipCode],
        });
      } else {
        // 更新现有图层的过滤器
        map.setFilter("search-highlight-fill", [
          "==",
          ["get", "zip_code"],
          zipCode,
        ]);
        map.setFilter("search-highlight-line", [
          "==",
          ["get", "zip_code"],
          zipCode,
        ]);
      }

      this.highlightedFeatures.add(zipCode);
    }
  }

  /**
   * 清除搜索高亮
   */
  clearSearchHighlight(): void {
    // 移除搜索标记
    if (this.searchMarker) {
      this.searchMarker.remove();
      this.searchMarker = null;
    }

    // 清除ZIP区域高亮
    this.clearZipHighlight();

    // 移除搜索标记样式
    this.removeSearchMarkerStyles();
  }

  /**
   * 清除ZIP区域高亮
   */
  private clearZipHighlight(): void {
    const map = this.mapManager.getMap();
    if (!map) {
      return;
    }

    // 移除高亮图层
    if (map.getLayer("search-highlight-fill")) {
      map.removeLayer("search-highlight-fill");
    }
    if (map.getLayer("search-highlight-line")) {
      map.removeLayer("search-highlight-line");
    }

    this.highlightedFeatures.clear();
  }

  /**
   * 添加搜索标记样式
   */
  private addSearchMarkerStyles(): void {
    if (typeof document === "undefined") {
      return;
    }

    const styleId = "search-marker-styles";
    if (document.getElementById(styleId)) {
      return;
    }

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      .search-marker {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .search-marker-inner {
        position: relative;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .search-marker-pulse {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: rgba(255, 107, 53, 0.3);
        animation: search-pulse 2s infinite;
      }

      .search-marker-icon {
        position: relative;
        z-index: 1;
        font-size: 20px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
      }

      @keyframes search-pulse {
        0% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.5);
          opacity: 0.5;
        }
        100% {
          transform: scale(2);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 移除搜索标记样式
   */
  private removeSearchMarkerStyles(): void {
    if (typeof document === "undefined") {
      return;
    }

    const style = document.getElementById("search-marker-styles");
    if (style) {
      style.remove();
    }
  }

  /**
   * 清理事件监听器
   */
  cleanup(): void {
    this.closePopup();
    this.clearSearchHighlight();
  }
}
